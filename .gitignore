# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Flutter build artifacts
app/
android/app/build/
ios/build/
macos/build/
windows/build/
linux/build/
web/build/

# Additional build directories
**/build/
**/intermediates/
**/generated/
**/outputs/
**/tmp/
**/.gradle/
**/kotlin/

# Node modules (if any)
node_modules/

# Backend build artifacts
back_app/node_modules/
back_apppp/node_modules/
